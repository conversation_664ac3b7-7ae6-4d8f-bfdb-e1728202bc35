
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import CharacterSelection from './CharacterSelection';
import StatsPage from './StatsPage';
import SettingsPage from './SettingsPage';
import TrophiesPage from './TrophiesPage';
import PlayerAccount from './PlayerAccount';
import { User, Trophy, BarChart3, Settings, Play, Crown, UserCog } from 'lucide-react';

interface MainMenuProps {
  onStartGame: () => void;
  onCustomize: () => void;
  onAccount?: () => void;
  highScore: number;
  selectedSprite: number;
  onSpriteSelect: (spriteId: number) => void;
}

type ActivePage = 'main' | 'characters' | 'stats' | 'settings' | 'trophies' | 'account';

const MainMenu = ({ onStartGame, onCustomize, onAccount, highScore, selectedSprite, onSpriteSelect }: MainMenuProps) => {
  const [activePage, setActivePage] = useState<ActivePage>('main');
  const [coins] = useState(2850);
  const [level] = useState(15);
  const [streak] = useState(7);

  const renderPage = () => {
    switch (activePage) {
      case 'characters':
        return (
          <CharacterSelection
            selectedSprite={selectedSprite}
            onSpriteSelect={onSpriteSelect}
            onBack={() => setActivePage('main')}
          />
        );
      case 'stats':
        return <StatsPage onBack={() => setActivePage('main')} />;
      case 'settings':
        return <SettingsPage onBack={() => setActivePage('main')} />;
      case 'trophies':
        return <TrophiesPage onBack={() => setActivePage('main')} />;
      case 'account':
        return <PlayerAccount onBack={() => setActivePage('main')} />;
      default:
        return null;
    }
  };

  if (activePage !== 'main') {
    return renderPage();
  }

  return (
    <div className="h-screen w-full relative overflow-hidden flex flex-col">
      {/* Video Background - Preloaded for Zero Delay */}
      <div className="absolute inset-0 w-full h-full">
        <video
          autoPlay
          loop
          muted
          playsInline
          preload="auto"
          className="w-full h-full object-cover"
          style={{
            objectFit: 'cover',
            objectPosition: 'center center'
          }}
          onLoadStart={() => console.log('Video loading started')}
          onCanPlay={() => console.log('Video ready to play')}
          onError={(e) => console.error('Video error:', e)}
        >
          <source src="/lovable-uploads/player_sprites/scrollfitt.mp4" type="video/mp4" />
          {/* Fallback background if video fails */}
          <div
            className="w-full h-full"
            style={{ background: '#4A9EFF' }}
          />
        </video>
      </div>

      {/* Video Overlay for Better Text Readability */}
      <div
        className="absolute inset-0"
        style={{
          background: 'linear-gradient(to bottom, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0.4) 100%)'
        }}
      />

      <div className="relative z-10 flex flex-col h-full">
        {/* Compact Header - Mobile Responsive */}
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          className="pt-4 sm:pt-6 px-3 sm:px-4 flex-shrink-0"
        >
          <div className="flex justify-between items-center mb-3 sm:mb-4">
            {/* Cartoon Logo - SCROLLFIT */}
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.8, type: "spring", bounce: 0.4 }}
              className="relative"
            >
              <h1
                className="text-4xl sm:text-6xl md:text-8xl font-black text-white relative tracking-wider"
                style={{
                  textShadow: '-3px -3px 0 #000, 3px -3px 0 #000, -3px 3px 0 #000, 3px 3px 0 #000, -2px -2px 0 #000, 2px -2px 0 #000, -2px 2px 0 #000, 2px 2px 0 #000, -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000',
                  WebkitTextStroke: '2px #000'
                }}
              >
                SCROLLFIT
                <motion.div
                  className="absolute -top-2 -right-8 sm:-right-12 text-2xl sm:text-4xl"
                  animate={{
                    rotate: [0, 15, -15, 0],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  🏈
                </motion.div>
              </h1>
            </motion.div>

            {/* Enhanced Profile Section - Mobile Responsive */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="flex items-center space-x-2 sm:space-x-3"
            >
              {/* Enhanced Settings Button */}
              <motion.button
                whileHover={{
                  scale: 1.15,
                  filter: 'brightness(1.2)'
                }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setActivePage('settings')}
                className="w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center border-2 border-[#00BCD4] relative overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, rgba(0, 188, 212, 0.3), rgba(0, 151, 167, 0.3))',
                  backdropFilter: 'blur(25px)'
                }}
              >
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 6, repeat: Infinity, ease: "linear" }}
                >
                  <Settings className="w-5 h-5 sm:w-6 sm:h-6 text-[#00BCD4]" />
                </motion.div>
              </motion.button>

              {/* Enhanced Account Button */}
              <motion.button
                whileHover={{
                  scale: 1.15,
                  filter: 'brightness(1.2)'
                }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setActivePage('account')}
                className="w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center border-3 border-[#39FF14] relative overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, rgba(57, 255, 20, 0.3), rgba(0, 212, 255, 0.3))',
                  backdropFilter: 'blur(25px)'
                }}
              >
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                >
                  <UserCog className="w-5 h-5 sm:w-6 sm:h-6 text-[#39FF14]" />
                </motion.div>
              </motion.button>

              {/* Enhanced Coins Display */}
              <motion.div
                whileHover={{
                  scale: 1.05
                }}
                className="flex items-center space-x-1 px-2 sm:px-3 py-1 sm:py-2 rounded-full border-2 border-[#FFD700]/40 relative"
                style={{
                  background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 107, 53, 0.2))',
                  backdropFilter: 'blur(20px)'
                }}
              >
                <motion.span
                  className="text-lg sm:text-xl"
                  animate={{
                    rotate: [0, 10, -10, 0],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  🪙
                </motion.span>
                <span className="text-[#FFD700] font-black text-xs sm:text-sm drop-shadow-lg">{coins.toLocaleString()}</span>
              </motion.div>

              {/* Enhanced Profile Avatar */}
              <motion.div
                whileHover={{
                  scale: 1.1
                }}
                className="relative w-10 h-10 sm:w-12 sm:h-12 rounded-full border-3 border-[#00D4FF] flex items-center justify-center overflow-hidden"
                style={{
                  background: 'linear-gradient(45deg, #00D4FF, #39FF14)'
                }}
              >
                <User className="w-5 h-5 sm:w-6 sm:h-6 text-black" />
                <motion.div
                  className="absolute -top-1 -right-1 w-5 h-5 sm:w-6 sm:h-6 bg-gradient-to-r from-[#FF6B35] to-[#F7931E] rounded-full flex items-center justify-center border-2 border-[#0A0B1A]"
                  animate={{
                    scale: [1, 1.2, 1]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <span className="text-xs font-black text-white">{level}</span>
                </motion.div>
              </motion.div>
            </motion.div>
          </div>

          {/* Enhanced Stats Section - Mobile Responsive */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3 }}
            className="flex justify-center items-center space-x-6 sm:space-x-8 mb-4 sm:mb-6"
          >
            <motion.div
              className="text-center p-3 rounded-xl border border-[#39FF14]/30"
              style={{
                background: 'linear-gradient(135deg, rgba(57, 255, 20, 0.1), rgba(0, 212, 255, 0.1))',
                backdropFilter: 'blur(20px)'
              }}
              whileHover={{ scale: 1.05 }}
            >
              <div className="text-[#39FF14] font-black text-lg sm:text-xl">{streak} Days</div>
              <div className="text-[#B8BCC8] text-xs font-bold">STREAK 🚀</div>
            </motion.div>
            <motion.div
              className="text-center p-3 rounded-xl border border-[#00D4FF]/30"
              style={{
                background: 'linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(57, 255, 20, 0.1))',
                backdropFilter: 'blur(20px)'
              }}
              whileHover={{ scale: 1.05 }}
            >
              <div className="text-[#00D4FF] font-black text-lg sm:text-xl">Level {level}</div>
              <div className="text-[#B8BCC8] text-xs font-bold">CHAMPION ⚡</div>
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Main Content - Mobile Responsive */}
        <div className="flex-1 px-3 sm:px-4 pb-3 sm:pb-4 flex flex-col justify-between">
          {/* MEGA Enhanced Play Button - Using Custom Button Image */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, type: "spring", bounce: 0.3 }}
            className="mb-8 relative"
          >
            <motion.button
              whileHover={{
                scale: 1.08,
                filter: 'brightness(1.2)'
              }}
              whileTap={{ scale: 0.95 }}
              onClick={onStartGame}
              className="w-full h-24 sm:h-28 rounded-3xl font-black text-2xl sm:text-3xl text-white relative overflow-hidden cursor-pointer border-6 border-black"
              style={{
                background: 'linear-gradient(145deg, #39FF14, #7CB342, #4CAF50)',
                boxShadow: '0 15px 0 #2E7D32, 0 25px 50px rgba(0, 0, 0, 0.3)',
                textShadow: '-3px -3px 0 #000, 3px -3px 0 #000, -3px 3px 0 #000, 3px 3px 0 #000',
                transform: 'translateY(0)',
                transition: 'all 0.2s ease'
              }}
              onMouseDown={(e) => {
                e.currentTarget.style.transform = 'translateY(10px)';
                e.currentTarget.style.boxShadow = '0 5px 0 #2E7D32, 0 15px 30px rgba(0, 0, 0, 0.3)';
              }}
              onMouseUp={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 15px 0 #2E7D32, 0 25px 50px rgba(0, 0, 0, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 15px 0 #2E7D32, 0 25px 50px rgba(0, 0, 0, 0.3)';
              }}
            >
              <div className="flex items-center justify-center space-x-4">
                <motion.span
                  animate={{
                    scale: [1, 1.3, 1],
                    rotate: [0, 15, -15, 0]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="text-4xl"
                >
                  🏈
                </motion.span>
                <span className="tracking-wider">PLAY GAME</span>
                <motion.span
                  animate={{
                    x: [0, 10, 0],
                    scale: [1, 1.2, 1]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="text-4xl"
                >
                  ⚡
                </motion.span>
              </div>

              {/* Dancing inner glow effect */}
              <motion.div
                className="absolute inset-0 rounded-3xl"
                animate={{
                  background: [
                    'radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4) 0%, transparent 50%)',
                    'radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.4) 0%, transparent 50%)',
                    'radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.4) 0%, transparent 50%)',
                    'radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.4) 0%, transparent 50%)',
                    'radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4) 0%, transparent 50%)'
                  ]
                }}
                transition={{ duration: 4, repeat: Infinity }}
              />

              {/* Pulsing border effect */}
              <motion.div
                className="absolute inset-0 rounded-3xl border-4 border-white"
                animate={{
                  opacity: [0.2, 0.8, 0.2],
                  scale: [0.98, 1.02, 0.98]
                }}
                transition={{ duration: 2.5, repeat: Infinity }}
              />
            </motion.button>

            {/* Floating particles around button */}
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-3 h-3 bg-[#39FF14] rounded-full"
                style={{
                  left: `${20 + i * 12}%`,
                  top: `${10 + (i % 2) * 80}%`,
                }}
                animate={{
                  y: [0, -20, 0],
                  opacity: [0.3, 1, 0.3],
                  scale: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 2 + i * 0.3,
                  repeat: Infinity,
                  delay: i * 0.2
                }}
              />
            ))}
          </motion.div>

          {/* Epic Gaming Cards Grid - Bigger & Better */}
          <div className="grid grid-cols-2 gap-4 sm:gap-6 mb-4 sm:mb-6">
            {/* Players Card - Purple Like Image */}
            <motion.button
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5, type: "spring", bounce: 0.3 }}
              whileHover={{
                scale: 1.08,
                boxShadow: '0 10px 0 #7B1FA2, 0 18px 35px rgba(0, 0, 0, 0.4)',
                filter: 'brightness(1.2)'
              }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActivePage('characters')}
              className="p-6 rounded-2xl font-black text-white h-32 sm:h-36 cursor-pointer relative overflow-hidden"
              style={{
                background: 'linear-gradient(145deg, #9C27B0, #8E24AA)', // Purple gradient
                border: '5px solid #000000',
                boxShadow: '0 8px 0 #7B1FA2, 0 12px 25px rgba(0, 0, 0, 0.3)',
                textShadow: '-2px -2px 0 #000, 2px -2px 0 #000, -2px 2px 0 #000, 2px 2px 0 #000',
                transform: 'translateY(0)',
                transition: 'all 0.2s ease'
              }}
              onMouseDown={(e) => {
                e.currentTarget.style.transform = 'translateY(4px)';
                e.currentTarget.style.boxShadow = '0 4px 0 #7B1FA2, 0 8px 15px rgba(0, 0, 0, 0.3)';
              }}
              onMouseUp={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 8px 0 #7B1FA2, 0 12px 25px rgba(0, 0, 0, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 8px 0 #7B1FA2, 0 12px 25px rgba(0, 0, 0, 0.3)';
              }}
            >
              <div className="flex flex-col items-center justify-center h-full">
                <motion.span
                  className="text-4xl mb-2"
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  👤
                </motion.span>
                <span className="text-sm font-black">PLAYERS</span>
              </div>
              {/* Inner glow */}
              <motion.div
                className="absolute inset-0 rounded-2xl"
                animate={{
                  boxShadow: [
                    'inset 0 0 15px rgba(255, 255, 255, 0.1)',
                    'inset 0 0 30px rgba(255, 255, 255, 0.3)',
                    'inset 0 0 15px rgba(255, 255, 255, 0.1)'
                  ]
                }}
                transition={{ duration: 3, repeat: Infinity }}
              />
            </motion.button>

            {/* Achievements Card - Orange Like Image */}
            <motion.button
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6, type: "spring", bounce: 0.3 }}
              whileHover={{
                scale: 1.08,
                boxShadow: '0 10px 0 #F57C00, 0 18px 35px rgba(0, 0, 0, 0.4)',
                filter: 'brightness(1.2)'
              }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActivePage('trophies')}
              className="p-6 rounded-2xl font-black text-white h-32 sm:h-36 cursor-pointer relative overflow-hidden"
              style={{
                background: 'linear-gradient(145deg, #FF9800, #FB8C00)', // Orange gradient
                border: '5px solid #000000',
                boxShadow: '0 8px 0 #F57C00, 0 12px 25px rgba(0, 0, 0, 0.3)',
                textShadow: '-2px -2px 0 #000, 2px -2px 0 #000, -2px 2px 0 #000, 2px 2px 0 #000',
                transform: 'translateY(0)',
                transition: 'all 0.2s ease'
              }}
              onMouseDown={(e) => {
                e.currentTarget.style.transform = 'translateY(4px)';
                e.currentTarget.style.boxShadow = '0 4px 0 #F57C00, 0 8px 15px rgba(0, 0, 0, 0.3)';
              }}
              onMouseUp={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 8px 0 #F57C00, 0 12px 25px rgba(0, 0, 0, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 8px 0 #F57C00, 0 12px 25px rgba(0, 0, 0, 0.3)';
              }}
            >
              <div className="flex flex-col items-center justify-center h-full">
                <motion.span
                  className="text-4xl mb-2"
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 10, -10, 0]
                  }}
                  transition={{ duration: 2.5, repeat: Infinity }}
                >
                  🏆
                </motion.span>
                <span className="text-sm font-black">ACHIEVEMENTS</span>
              </div>
              {/* Inner glow */}
              <motion.div
                className="absolute inset-0 rounded-2xl"
                animate={{
                  boxShadow: [
                    'inset 0 0 15px rgba(255, 255, 255, 0.1)',
                    'inset 0 0 30px rgba(255, 255, 255, 0.3)',
                    'inset 0 0 15px rgba(255, 255, 255, 0.1)'
                  ]
                }}
                transition={{ duration: 3, repeat: Infinity }}
              />
            </motion.button>

            {/* Stats Card - Red Like Image */}
            <motion.button
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.7, type: "spring", bounce: 0.3 }}
              whileHover={{
                scale: 1.08,
                boxShadow: '0 10px 0 #D32F2F, 0 18px 35px rgba(0, 0, 0, 0.4)',
                filter: 'brightness(1.2)'
              }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActivePage('stats')}
              className="p-6 rounded-2xl font-black text-white h-32 sm:h-36 cursor-pointer relative overflow-hidden"
              style={{
                background: 'linear-gradient(145deg, #F44336, #E53935)', // Red gradient
                border: '5px solid #000000',
                boxShadow: '0 8px 0 #D32F2F, 0 12px 25px rgba(0, 0, 0, 0.3)',
                textShadow: '-2px -2px 0 #000, 2px -2px 0 #000, -2px 2px 0 #000, 2px 2px 0 #000',
                transform: 'translateY(0)',
                transition: 'all 0.2s ease'
              }}
              onMouseDown={(e) => {
                e.currentTarget.style.transform = 'translateY(4px)';
                e.currentTarget.style.boxShadow = '0 4px 0 #D32F2F, 0 8px 15px rgba(0, 0, 0, 0.3)';
              }}
              onMouseUp={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 8px 0 #D32F2F, 0 12px 25px rgba(0, 0, 0, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 8px 0 #D32F2F, 0 12px 25px rgba(0, 0, 0, 0.3)';
              }}
            >
              <div className="flex flex-col items-center justify-center h-full">
                <motion.span
                  className="text-4xl mb-2"
                  animate={{
                    y: [0, -3, 0],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  📊
                </motion.span>
                <span className="text-sm font-black">STATS</span>
              </div>
              {/* Inner glow */}
              <motion.div
                className="absolute inset-0 rounded-2xl"
                animate={{
                  boxShadow: [
                    'inset 0 0 15px rgba(255, 255, 255, 0.1)',
                    'inset 0 0 30px rgba(255, 255, 255, 0.3)',
                    'inset 0 0 15px rgba(255, 255, 255, 0.1)'
                  ]
                }}
                transition={{ duration: 3, repeat: Infinity }}
              />
            </motion.button>

            {/* Settings Card - Cyan Like Image */}
            <motion.button
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.8, type: "spring", bounce: 0.3 }}
              whileHover={{
                scale: 1.08,
                boxShadow: '0 10px 0 #0097A7, 0 18px 35px rgba(0, 0, 0, 0.4)',
                filter: 'brightness(1.2)'
              }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActivePage('settings')}
              className="p-6 rounded-2xl font-black text-white h-32 sm:h-36 cursor-pointer relative overflow-hidden"
              style={{
                background: 'linear-gradient(145deg, #00BCD4, #00ACC1)', // Cyan gradient
                border: '5px solid #000000',
                boxShadow: '0 8px 0 #0097A7, 0 12px 25px rgba(0, 0, 0, 0.3)',
                textShadow: '-2px -2px 0 #000, 2px -2px 0 #000, -2px 2px 0 #000, 2px 2px 0 #000',
                transform: 'translateY(0)',
                transition: 'all 0.2s ease'
              }}
              onMouseDown={(e) => {
                e.currentTarget.style.transform = 'translateY(4px)';
                e.currentTarget.style.boxShadow = '0 4px 0 #0097A7, 0 8px 15px rgba(0, 0, 0, 0.3)';
              }}
              onMouseUp={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 8px 0 #0097A7, 0 12px 25px rgba(0, 0, 0, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 8px 0 #0097A7, 0 12px 25px rgba(0, 0, 0, 0.3)';
              }}
            >
              <div className="flex flex-col items-center justify-center h-full">
                <motion.span
                  className="text-4xl mb-2"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 6, repeat: Infinity, ease: "linear" }}
                >
                  ⚙️
                </motion.span>
                <span className="text-sm font-black">SETTINGS</span>
              </div>
              {/* Inner glow */}
              <motion.div
                className="absolute inset-0 rounded-2xl"
                animate={{
                  boxShadow: [
                    'inset 0 0 15px rgba(255, 255, 255, 0.1)',
                    'inset 0 0 30px rgba(255, 255, 255, 0.3)',
                    'inset 0 0 15px rgba(255, 255, 255, 0.1)'
                  ]
                }}
                transition={{ duration: 3, repeat: Infinity }}
              />
            </motion.button>
          </div>

          {/* Compact High Score Display */}
          {highScore > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9 }}
              className="text-center"
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="inline-flex items-center space-x-3 px-6 py-3 rounded-full border-2 border-[#FFD700]/30 relative"
                style={{
                  background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 107, 53, 0.1))',
                  backdropFilter: 'blur(25px)'
                }}
              >
                <motion.div
                  animate={{ rotate: [0, 15, -15, 0] }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  <Crown className="w-6 h-6 text-[#FFD700]" />
                </motion.div>
                <div>
                  <div className="text-[#FFD700] text-xs font-bold uppercase tracking-wider">
                    CHAMPION RECORD
                  </div>
                  <div
                    className="text-xl font-black text-white"
                  >
                    {highScore.toLocaleString()}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MainMenu;
