
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import CharacterSelection from './CharacterSelection';
import StatsPage from './StatsPage';
import SettingsPage from './SettingsPage';
import TrophiesPage from './TrophiesPage';
import PlayerAccount from './PlayerAccount';
import { User, Trophy, BarChart3, Settings, Play, Crown, UserCog } from 'lucide-react';

interface MainMenuProps {
  onStartGame: () => void;
  onCustomize: () => void;
  onAccount?: () => void;
  highScore: number;
  selectedSprite: number;
  onSpriteSelect: (spriteId: number) => void;
}

type ActivePage = 'main' | 'characters' | 'stats' | 'settings' | 'trophies' | 'account';

const MainMenu = ({ onStartGame, onCustomize, onAccount, highScore, selectedSprite, onSpriteSelect }: MainMenuProps) => {
  const [activePage, setActivePage] = useState<ActivePage>('main');
  const [coins] = useState(2850);
  const [level] = useState(15);
  const [streak] = useState(7);

  const renderPage = () => {
    switch (activePage) {
      case 'characters':
        return (
          <CharacterSelection
            selectedSprite={selectedSprite}
            onSpriteSelect={onSpriteSelect}
            onBack={() => setActivePage('main')}
          />
        );
      case 'stats':
        return <StatsPage onBack={() => setActivePage('main')} />;
      case 'settings':
        return <SettingsPage onBack={() => setActivePage('main')} />;
      case 'trophies':
        return <TrophiesPage onBack={() => setActivePage('main')} />;
      case 'account':
        return <PlayerAccount onBack={() => setActivePage('main')} />;
      default:
        return null;
    }
  };

  if (activePage !== 'main') {
    return renderPage();
  }

  return (
    <div className="h-screen w-full relative overflow-hidden flex flex-col">
      {/* Video Background - Preloaded for Zero Delay */}
      <div className="absolute inset-0 w-full h-full">
        <video
          autoPlay
          loop
          muted
          playsInline
          preload="auto"
          className="w-full h-full object-cover"
          style={{
            objectFit: 'cover',
            objectPosition: 'center center'
          }}
          onLoadStart={() => console.log('Video loading started')}
          onCanPlay={() => console.log('Video ready to play')}
          onError={(e) => console.error('Video error:', e)}
        >
          <source src="/lovable-uploads/player_sprites/scrollfitt.mp4" type="video/mp4" />
          {/* Fallback background if video fails */}
          <div
            className="w-full h-full"
            style={{ background: '#4A9EFF' }}
          />
        </video>
      </div>

      {/* Video Overlay for Better Text Readability */}
      <div
        className="absolute inset-0"
        style={{
          background: 'linear-gradient(to bottom, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0.4) 100%)'
        }}
      />

      <div className="relative z-10 flex flex-col h-full">
        {/* Compact Header - Mobile Responsive */}
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          className="pt-4 sm:pt-6 px-3 sm:px-4 flex-shrink-0"
        >
          <div className="flex justify-between items-center mb-3 sm:mb-4">
            {/* Cartoon Logo - SCROLLFIT */}
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.8, type: "spring", bounce: 0.4 }}
              className="relative"
            >
              <h1
                className="text-4xl sm:text-6xl md:text-8xl font-black text-white relative tracking-wider"
                style={{
                  textShadow: '-3px -3px 0 #000, 3px -3px 0 #000, -3px 3px 0 #000, 3px 3px 0 #000, -2px -2px 0 #000, 2px -2px 0 #000, -2px 2px 0 #000, 2px 2px 0 #000, -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000',
                  WebkitTextStroke: '2px #000'
                }}
              >
                SCROLLFIT
                <motion.div
                  className="absolute -top-2 -right-8 sm:-right-12 text-2xl sm:text-4xl"
                  animate={{
                    rotate: [0, 15, -15, 0],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  🏈
                </motion.div>
              </h1>
            </motion.div>

            {/* Compact Profile Section - Mobile Responsive */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="flex items-center space-x-1 sm:space-x-2"
            >
              {/* Enhanced Account Button */}
              <motion.button
                whileHover={{
                  scale: 1.15,
                  boxShadow: '0 0 30px rgba(57, 255, 20, 0.8)',
                  filter: 'brightness(1.2)'
                }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setActivePage('account')}
                className="w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center border-3 border-[#39FF14] relative overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, rgba(57, 255, 20, 0.3), rgba(0, 212, 255, 0.3))',
                  backdropFilter: 'blur(25px)',
                  boxShadow: '0 4px 20px rgba(57, 255, 20, 0.4)'
                }}
              >
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                >
                  <UserCog className="w-5 h-5 sm:w-6 sm:h-6 text-[#39FF14]" />
                </motion.div>

                {/* Pulsing ring effect */}
                <motion.div
                  className="absolute inset-0 rounded-full border-2 border-[#39FF14]"
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [0.5, 0, 0.5]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                />
              </motion.button>

              {/* Compact Coins Display */}
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="flex items-center space-x-1 px-2 sm:px-3 py-1 sm:py-2 rounded-full border border-[#FFD700]/30 relative"
                style={{
                  background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 107, 53, 0.1))',
                  backdropFilter: 'blur(20px)'
                }}
              >
                <span className="text-lg sm:text-xl">🪙</span>
                <span className="text-[#FFD700] font-black text-xs sm:text-sm">{coins.toLocaleString()}</span>
              </motion.div>

              {/* Compact Profile Avatar */}
              <motion.div
                whileHover={{ scale: 1.1 }}
                className="relative w-10 h-10 sm:w-12 sm:h-12 rounded-full border-2 border-[#00D4FF] flex items-center justify-center overflow-hidden"
                style={{
                  background: 'linear-gradient(45deg, #00D4FF, #39FF14)',
                  boxShadow: '0 0 20px rgba(0, 212, 255, 0.4)'
                }}
              >
                <User className="w-5 h-5 sm:w-6 sm:h-6 text-black" />
                <motion.div
                  className="absolute -top-1 -right-1 w-5 h-5 sm:w-6 sm:h-6 bg-gradient-to-r from-[#FF6B35] to-[#F7931E] rounded-full flex items-center justify-center border-2 border-[#0A0B1A]"
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <span className="text-xs font-black text-white">{level}</span>
                </motion.div>
              </motion.div>
            </motion.div>
          </div>

          {/* Enhanced Welcome Section - Mobile Responsive */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3 }}
            className="text-center mb-4 sm:mb-6 p-4 sm:p-6 rounded-2xl border-2 border-[#39FF14]/30"
            style={{
              background: 'linear-gradient(135deg, rgba(57, 255, 20, 0.1), rgba(0, 212, 255, 0.1))',
              backdropFilter: 'blur(20px)',
              boxShadow: '0 8px 32px rgba(57, 255, 20, 0.2)'
            }}
          >
            <motion.h2
              className="text-xl sm:text-2xl md:text-3xl font-black text-white mb-3"
              animate={{
                textShadow: [
                  '0 0 10px rgba(57, 255, 20, 0.5)',
                  '0 0 20px rgba(57, 255, 20, 0.8)',
                  '0 0 10px rgba(57, 255, 20, 0.5)'
                ]
              }}
              transition={{ duration: 2, repeat: Infinity }}
              style={{
                textShadow: '-2px -2px 0 #000, 2px -2px 0 #000, -2px 2px 0 #000, 2px 2px 0 #000'
              }}
            >
              READY TO DOMINATE? 🔥
            </motion.h2>
            <div className="flex justify-center items-center space-x-6 sm:space-x-8">
              <motion.div
                className="text-center"
                whileHover={{ scale: 1.1 }}
              >
                <div className="text-[#39FF14] font-black text-lg sm:text-xl">{streak} Days</div>
                <div className="text-[#B8BCC8] text-sm font-bold">STREAK 🚀</div>
              </motion.div>
              <motion.div
                className="text-center"
                whileHover={{ scale: 1.1 }}
              >
                <div className="text-[#00D4FF] font-black text-lg sm:text-xl">Level {level}</div>
                <div className="text-[#B8BCC8] text-sm font-bold">CHAMPION ⚡</div>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>

        {/* Main Content - Mobile Responsive */}
        <div className="flex-1 px-3 sm:px-4 pb-3 sm:pb-4 flex flex-col justify-between">
          {/* Epic Enhanced Play Button */}
          <motion.button
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, type: "spring", bounce: 0.3 }}
            whileHover={{
              scale: 1.05,
              boxShadow: '0 15px 0 #D32F2F, 0 25px 50px rgba(244, 67, 54, 0.7)',
              filter: 'brightness(1.3) drop-shadow(0 0 40px rgba(244, 67, 54, 1))'
            }}
            whileTap={{ scale: 0.98 }}
            onClick={onStartGame}
            className="w-full p-10 rounded-3xl font-black text-3xl sm:text-4xl text-white mb-8 relative overflow-hidden cursor-pointer"
            style={{
              background: 'linear-gradient(145deg, #F44336, #E53935, #D32F2F)', // Enhanced red gradient
              border: '8px solid #000000',
              boxShadow: '0 12px 0 #D32F2F, 0 20px 40px rgba(0, 0, 0, 0.5)',
              textShadow: '-4px -4px 0 #000, 4px -4px 0 #000, -4px 4px 0 #000, 4px 4px 0 #000',
              transform: 'translateY(0)',
              transition: 'all 0.2s ease'
            }}
            onMouseDown={(e) => {
              e.currentTarget.style.transform = 'translateY(8px)';
              e.currentTarget.style.boxShadow = '0 4px 0 #D32F2F, 0 10px 25px rgba(0, 0, 0, 0.5)';
            }}
            onMouseUp={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 12px 0 #D32F2F, 0 20px 40px rgba(0, 0, 0, 0.5)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 12px 0 #D32F2F, 0 20px 40px rgba(0, 0, 0, 0.5)';
            }}
          >
            <div className="flex items-center justify-center space-x-4">
              <motion.span
                animate={{
                  scale: [1, 1.2, 1],
                  rotate: [0, 10, -10, 0]
                }}
                transition={{ duration: 2, repeat: Infinity }}
                className="text-3xl"
              >
                🎮
              </motion.span>
              <span>PLAY GAME</span>
              <motion.span
                animate={{
                  x: [0, 8, 0],
                  rotate: [0, 15, 0]
                }}
                transition={{ duration: 2, repeat: Infinity }}
                className="text-3xl"
              >
                ▶️
              </motion.span>
            </div>

            {/* Enhanced inner glow effect */}
            <motion.div
              className="absolute inset-0 rounded-3xl"
              animate={{
                boxShadow: [
                  'inset 0 0 30px rgba(255, 255, 255, 0.3)',
                  'inset 0 0 60px rgba(255, 255, 255, 0.6)',
                  'inset 0 0 30px rgba(255, 255, 255, 0.3)'
                ]
              }}
              transition={{ duration: 2.5, repeat: Infinity }}
            />

            {/* Additional pulsing effect */}
            <motion.div
              className="absolute inset-0 rounded-3xl"
              animate={{
                background: [
                  'radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%)',
                  'radial-gradient(circle at center, rgba(255, 255, 255, 0.3) 0%, transparent 70%)',
                  'radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%)'
                ]
              }}
              transition={{ duration: 3, repeat: Infinity, delay: 1 }}
            />
          </motion.button>

          {/* Epic Gaming Cards Grid - Bigger & Better */}
          <div className="grid grid-cols-2 gap-4 sm:gap-6 mb-4 sm:mb-6">
            {/* Players Card - Purple Like Image */}
            <motion.button
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5, type: "spring", bounce: 0.3 }}
              whileHover={{
                scale: 1.08,
                boxShadow: '0 10px 0 #7B1FA2, 0 18px 35px rgba(156, 39, 176, 0.6)',
                filter: 'brightness(1.2) drop-shadow(0 0 25px rgba(156, 39, 176, 0.8))'
              }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActivePage('characters')}
              className="p-6 rounded-2xl font-black text-white h-32 sm:h-36 cursor-pointer relative overflow-hidden"
              style={{
                background: 'linear-gradient(145deg, #9C27B0, #8E24AA)', // Purple gradient
                border: '5px solid #000000',
                boxShadow: '0 8px 0 #7B1FA2, 0 12px 25px rgba(0, 0, 0, 0.3)',
                textShadow: '-2px -2px 0 #000, 2px -2px 0 #000, -2px 2px 0 #000, 2px 2px 0 #000',
                transform: 'translateY(0)',
                transition: 'all 0.2s ease'
              }}
              onMouseDown={(e) => {
                e.currentTarget.style.transform = 'translateY(4px)';
                e.currentTarget.style.boxShadow = '0 4px 0 #7B1FA2, 0 8px 15px rgba(0, 0, 0, 0.3)';
              }}
              onMouseUp={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 8px 0 #7B1FA2, 0 12px 25px rgba(0, 0, 0, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 8px 0 #7B1FA2, 0 12px 25px rgba(0, 0, 0, 0.3)';
              }}
            >
              <div className="flex flex-col items-center justify-center h-full">
                <motion.span
                  className="text-4xl mb-2"
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  👤
                </motion.span>
                <span className="text-sm font-black">PLAYERS</span>
              </div>
              {/* Inner glow */}
              <motion.div
                className="absolute inset-0 rounded-2xl"
                animate={{
                  boxShadow: [
                    'inset 0 0 15px rgba(255, 255, 255, 0.1)',
                    'inset 0 0 30px rgba(255, 255, 255, 0.3)',
                    'inset 0 0 15px rgba(255, 255, 255, 0.1)'
                  ]
                }}
                transition={{ duration: 3, repeat: Infinity }}
              />
            </motion.button>

            {/* Achievements Card - Orange Like Image */}
            <motion.button
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6, type: "spring", bounce: 0.3 }}
              whileHover={{
                scale: 1.08,
                boxShadow: '0 10px 0 #F57C00, 0 18px 35px rgba(255, 152, 0, 0.6)',
                filter: 'brightness(1.2) drop-shadow(0 0 25px rgba(255, 152, 0, 0.8))'
              }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActivePage('trophies')}
              className="p-6 rounded-2xl font-black text-white h-32 sm:h-36 cursor-pointer relative overflow-hidden"
              style={{
                background: 'linear-gradient(145deg, #FF9800, #FB8C00)', // Orange gradient
                border: '5px solid #000000',
                boxShadow: '0 8px 0 #F57C00, 0 12px 25px rgba(0, 0, 0, 0.3)',
                textShadow: '-2px -2px 0 #000, 2px -2px 0 #000, -2px 2px 0 #000, 2px 2px 0 #000',
                transform: 'translateY(0)',
                transition: 'all 0.2s ease'
              }}
              onMouseDown={(e) => {
                e.currentTarget.style.transform = 'translateY(4px)';
                e.currentTarget.style.boxShadow = '0 4px 0 #F57C00, 0 8px 15px rgba(0, 0, 0, 0.3)';
              }}
              onMouseUp={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 8px 0 #F57C00, 0 12px 25px rgba(0, 0, 0, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 8px 0 #F57C00, 0 12px 25px rgba(0, 0, 0, 0.3)';
              }}
            >
              <div className="flex flex-col items-center justify-center h-full">
                <motion.span
                  className="text-4xl mb-2"
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 10, -10, 0]
                  }}
                  transition={{ duration: 2.5, repeat: Infinity }}
                >
                  🏆
                </motion.span>
                <span className="text-sm font-black">ACHIEVEMENTS</span>
              </div>
              {/* Inner glow */}
              <motion.div
                className="absolute inset-0 rounded-2xl"
                animate={{
                  boxShadow: [
                    'inset 0 0 15px rgba(255, 255, 255, 0.1)',
                    'inset 0 0 30px rgba(255, 255, 255, 0.3)',
                    'inset 0 0 15px rgba(255, 255, 255, 0.1)'
                  ]
                }}
                transition={{ duration: 3, repeat: Infinity }}
              />
            </motion.button>

            {/* Stats Card - Red Like Image */}
            <motion.button
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.7, type: "spring", bounce: 0.3 }}
              whileHover={{
                scale: 1.08,
                boxShadow: '0 10px 0 #D32F2F, 0 18px 35px rgba(244, 67, 54, 0.6)',
                filter: 'brightness(1.2) drop-shadow(0 0 25px rgba(244, 67, 54, 0.8))'
              }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActivePage('stats')}
              className="p-6 rounded-2xl font-black text-white h-32 sm:h-36 cursor-pointer relative overflow-hidden"
              style={{
                background: 'linear-gradient(145deg, #F44336, #E53935)', // Red gradient
                border: '5px solid #000000',
                boxShadow: '0 8px 0 #D32F2F, 0 12px 25px rgba(0, 0, 0, 0.3)',
                textShadow: '-2px -2px 0 #000, 2px -2px 0 #000, -2px 2px 0 #000, 2px 2px 0 #000',
                transform: 'translateY(0)',
                transition: 'all 0.2s ease'
              }}
              onMouseDown={(e) => {
                e.currentTarget.style.transform = 'translateY(4px)';
                e.currentTarget.style.boxShadow = '0 4px 0 #D32F2F, 0 8px 15px rgba(0, 0, 0, 0.3)';
              }}
              onMouseUp={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 8px 0 #D32F2F, 0 12px 25px rgba(0, 0, 0, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 8px 0 #D32F2F, 0 12px 25px rgba(0, 0, 0, 0.3)';
              }}
            >
              <div className="flex flex-col items-center justify-center h-full">
                <motion.span
                  className="text-4xl mb-2"
                  animate={{
                    y: [0, -3, 0],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  📊
                </motion.span>
                <span className="text-sm font-black">STATS</span>
              </div>
              {/* Inner glow */}
              <motion.div
                className="absolute inset-0 rounded-2xl"
                animate={{
                  boxShadow: [
                    'inset 0 0 15px rgba(255, 255, 255, 0.1)',
                    'inset 0 0 30px rgba(255, 255, 255, 0.3)',
                    'inset 0 0 15px rgba(255, 255, 255, 0.1)'
                  ]
                }}
                transition={{ duration: 3, repeat: Infinity }}
              />
            </motion.button>

            {/* Settings Card - Cyan Like Image */}
            <motion.button
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.8, type: "spring", bounce: 0.3 }}
              whileHover={{
                scale: 1.08,
                boxShadow: '0 10px 0 #0097A7, 0 18px 35px rgba(0, 188, 212, 0.6)',
                filter: 'brightness(1.2) drop-shadow(0 0 25px rgba(0, 188, 212, 0.8))'
              }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActivePage('settings')}
              className="p-6 rounded-2xl font-black text-white h-32 sm:h-36 cursor-pointer relative overflow-hidden"
              style={{
                background: 'linear-gradient(145deg, #00BCD4, #00ACC1)', // Cyan gradient
                border: '5px solid #000000',
                boxShadow: '0 8px 0 #0097A7, 0 12px 25px rgba(0, 0, 0, 0.3)',
                textShadow: '-2px -2px 0 #000, 2px -2px 0 #000, -2px 2px 0 #000, 2px 2px 0 #000',
                transform: 'translateY(0)',
                transition: 'all 0.2s ease'
              }}
              onMouseDown={(e) => {
                e.currentTarget.style.transform = 'translateY(4px)';
                e.currentTarget.style.boxShadow = '0 4px 0 #0097A7, 0 8px 15px rgba(0, 0, 0, 0.3)';
              }}
              onMouseUp={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 8px 0 #0097A7, 0 12px 25px rgba(0, 0, 0, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 8px 0 #0097A7, 0 12px 25px rgba(0, 0, 0, 0.3)';
              }}
            >
              <div className="flex flex-col items-center justify-center h-full">
                <motion.span
                  className="text-4xl mb-2"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 6, repeat: Infinity, ease: "linear" }}
                >
                  ⚙️
                </motion.span>
                <span className="text-sm font-black">SETTINGS</span>
              </div>
              {/* Inner glow */}
              <motion.div
                className="absolute inset-0 rounded-2xl"
                animate={{
                  boxShadow: [
                    'inset 0 0 15px rgba(255, 255, 255, 0.1)',
                    'inset 0 0 30px rgba(255, 255, 255, 0.3)',
                    'inset 0 0 15px rgba(255, 255, 255, 0.1)'
                  ]
                }}
                transition={{ duration: 3, repeat: Infinity }}
              />
            </motion.button>
          </div>

          {/* Compact High Score Display */}
          {highScore > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9 }}
              className="text-center"
            >
              <motion.div 
                whileHover={{ scale: 1.05 }}
                className="inline-flex items-center space-x-3 px-6 py-3 rounded-full border-2 border-[#FFD700]/30 relative"
                style={{
                  background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 107, 53, 0.1))',
                  backdropFilter: 'blur(25px)',
                  boxShadow: '0 15px 50px rgba(255, 215, 0, 0.3)'
                }}
              >
                <motion.div
                  animate={{ rotate: [0, 15, -15, 0] }}
                  transition={{ duration: 3, repeat: Infinity }}
                >
                  <Crown className="w-6 h-6 text-[#FFD700]" />
                </motion.div>
                <div>
                  <div className="text-[#FFD700] text-xs font-bold uppercase tracking-wider">
                    CHAMPION RECORD
                  </div>
                  <div 
                    className="text-xl font-black text-white"
                    style={{ textShadow: '0 0 25px #FFD700' }}
                  >
                    {highScore.toLocaleString()}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MainMenu;
